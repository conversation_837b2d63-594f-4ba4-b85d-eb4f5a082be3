'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../app/context/AuthContext';
import { AgentOutput, getAgentOutputs } from '../../lib/firebase/agentOutputs';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Skeleton } from '../ui/skeleton';
import { Clock, Download, FileText, Search, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { ScrollArea } from '../ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';

/**
 * PMO Agent Outputs Tab Component
 *
 * Displays a list of PMO agent outputs with filtering and viewing capabilities.
 */
export default function AgentOutputsTab() {
  const { user } = useAuth();
  const [outputs, setOutputs] = useState<AgentOutput[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOutput, setSelectedOutput] = useState<AgentOutput | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch agent outputs
  useEffect(() => {
    async function fetchOutputs() {
      if (!user?.email) return;

      setLoading(true);
      setError(null);

      try {
        const fetchedOutputs = await getAgentOutputs(user.email, 'PMO');
        setOutputs(fetchedOutputs);
      } catch (err: any) {
        console.error('Error fetching PMO agent outputs:', err);
        setError(err.message || 'Failed to fetch PMO agent outputs');
      } finally {
        setLoading(false);
      }
    }

    fetchOutputs();
  }, [user]);

  // Filter outputs based on search term
  const filteredOutputs = outputs.filter(output =>
    output.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    output.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle output selection
  const handleOutputSelect = (output: AgentOutput) => {
    setSelectedOutput(output);
    setIsDialogOpen(true);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  // Format date
  const formatDate = (date: Date) => {
    return formatDistanceToNow(date, { addSuffix: true });
  };

  // Render loading state
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">PMO Agent Outputs</h2>
          <Skeleton className="h-10 w-64" />
        </div>
        {[1, 2, 3].map(i => (
          <Skeleton key={i} className="h-40 w-full" />
        ))}
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-800 rounded-md text-red-800 dark:text-red-300">
        <h2 className="text-lg font-semibold mb-2">Error Loading Outputs</h2>
        <p>{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">PMO Output</h2>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search outputs..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <Button
              variant="ghost"
              className="absolute right-0 top-0 h-full aspect-square p-0"
              onClick={() => setSearchTerm('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {filteredOutputs.length === 0 ? (
        <div className="p-8 text-center border border-dashed rounded-md">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Documents Found</h3>
          <p className="text-muted-foreground">
            {outputs.length === 0
              ? "No PMO requirements documents have been generated yet."
              : "No documents match your search criteria."}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredOutputs.map(output => (
            <Card key={output.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg truncate">{output.title}</CardTitle>
                <CardDescription className="flex items-center text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDate(output.createdAt)}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <p className="text-sm line-clamp-3">
                  {output.content.substring(0, 150)}...
                </p>
              </CardContent>
              <CardFooter className="flex justify-between pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleOutputSelect(output)}
                >
                  View Details
                </Button>
                {output.fileUrl && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(output.fileUrl, '_blank')}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    PDF
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Document Viewer Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>{selectedOutput?.title}</DialogTitle>
            <DialogDescription className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {selectedOutput && formatDate(selectedOutput.createdAt)}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs defaultValue="content">
              <TabsList>
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="h-full">
                <ScrollArea className="h-[calc(80vh-10rem)] border rounded-md p-4">
                  <div className="prose dark:prose-invert max-w-none">
                    {selectedOutput?.content.split('\n').map((line, i) => (
                      <React.Fragment key={i}>
                        {line}
                        <br />
                      </React.Fragment>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="metadata" className="h-full">
                <ScrollArea className="h-[calc(80vh-10rem)] border rounded-md p-4">
                  {selectedOutput?.metadata ? (
                    <div className="space-y-2">
                      {Object.entries(selectedOutput.metadata).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 gap-2 py-1 border-b">
                          <div className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</div>
                          <div className="col-span-2">{String(value)}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No metadata available</p>
                  )}
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            {selectedOutput?.fileUrl && (
              <Button
                onClick={() => window.open(selectedOutput.fileUrl, '_blank')}
              >
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            )}
            <Button variant="outline" onClick={handleDialogClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
