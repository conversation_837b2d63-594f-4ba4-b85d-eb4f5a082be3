/**
 * PMO Agent
 *
 * This agent manages tasks across five Agentic Teams:
 * - Marketing (Ag001)
 * - Research (Ag002)
 * - Software Design (Ag003)
 * - Sales (Ag004)
 * - Business Analysis (Ag005)
 *
 * It analyzes tasks, selects appropriate teams, creates PMO records,
 * and assigns tasks to the selected teams.
 */

import { simplePromptOptimizer } from '../../tools/simplePromptOptimizer';
import {
  PMOAgentOptions,
  PMOFormInput,
  PMOAgentResult,
  PMORecord,
  PMOStreamUpdate,
  TaskAnalysisResult,
  AgenticTeamId
} from './PMOInterfaces';
import { addPMORecord } from '../../firebase/pmo';
import { Project, Task } from 'admin/planner/types';

export class PMOAgent {
  private options: PMOAgentOptions;
  private plannerContext: any;

  constructor(options: PMOAgentOptions) {
    this.options = {
      userId: options.userId,
      includeExplanation: options.includeExplanation || false,
      streamResponse: options.streamResponse || false,
      onStreamUpdate: options.onStreamUpdate
    };

    // The plannerContext will be injected when the agent is used
    this.plannerContext = null;
  }

  /**
   * Set the planner context for project and task operations
   * @param context - The planner context from usePlanner()
   */
  public setPlannerContext(context: any) {
    this.plannerContext = context;
  }

  /**
   * Process a PMO request
   * @param input - The PMO form input
   * @returns - The PMO agent result
   */
  public async processRequest(input: PMOFormInput): Promise<PMOAgentResult> {
    try {
      // Update stream if enabled
      this._updateStream('analyzing-task', {
        input
      }, 'Analyzing task and determining requirements...');

      // Step 1: Analyze the task
      const taskAnalysis = await this._analyzeTask(input);

      // Update stream if enabled
      this._updateStream('selecting-team', {
        taskAnalysis
      }, 'Selecting appropriate teams for the task...');

      // Step 2: Select teams (this is now part of task analysis)
      const selectedTeams = taskAnalysis.selectedTeams;

      // Update stream if enabled
      this._updateStream('creating-pmo-record', {
        selectedTeams,
        taskAnalysis
      }, 'Creating PMO record and project...');

      // Step 3: Create a project if needed
      let projectId = input.projectId || '';

      if (!projectId) {
        // Check if planner context is available
        if (!this.plannerContext) {
          throw new Error('PlannerContext is not set. Call setPlannerContext before processing requests.');
        }

        // Create a new project using the planner context
        const project: Omit<Project, 'id'> = {
          name: `PMO: ${input.title}`,
          description: input.description,
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          owner: this.options.userId,
          members: [this.options.userId],
          categories: selectedTeams.map(team => {
            switch (team) {
              case AgenticTeamId.Marketing:
                return 'Marketing';
              case AgenticTeamId.Research:
                return 'Research';
              case AgenticTeamId.SoftwareDesign:
                return 'Design';
              case AgenticTeamId.Sales:
                return 'Sales';
              case AgenticTeamId.BusinessAnalysis:
                return 'Admin';
              default:
                return 'Other';
            }
          }),
          status: 'Active'
        };

        // Use the createProject method from the planner context
        projectId = await this.plannerContext.createProject(project);
      }

      // Step 4: Create PMO record
      const pmoRecord: Omit<PMORecord, 'id'> = {
        title: input.title,
        description: input.description,
        status: 'In Progress',
        priority: input.priority,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: this.options.userId,
        agentIds: selectedTeams,
        projectIds: projectId ? [projectId] : [],
        taskIds: [],
        category: input.category || 'Unknown',
        sourceFile: input.sourceFile,
        fileName: input.fileName,
        contextFiles: input.contextOptions.fileIds,
        contextCategories: input.contextOptions.categoryIds,
        customContext: input.contextOptions.customContext,
        summary: taskAnalysis.formalizedTask,
        teamSelectionRationale: taskAnalysis.teamSelectionRationale,
        resourceRecommendations: taskAnalysis.recommendedResources
      };

      const pmoRecordId = await addPMORecord(pmoRecord);

      // Update stream if enabled
      this._updateStream('assigning-tasks', {
        pmoRecordId,
        projectId,
        taskAnalysis
      }, 'Creating and assigning tasks to teams...');

      // Step 5: Create tasks for each team
      const createdTasks: Task[] = [];
      const taskIds: string[] = [];

      // Create a task for each breakdown item
      for (let i = 0; i < taskAnalysis.breakdown.length; i++) {
        const breakdownItem = taskAnalysis.breakdown[i];

        // Determine which team should handle this task
        // For simplicity, we'll assign tasks in order to the selected teams
        const teamIndex = i % selectedTeams.length;
        const assignedTeam = selectedTeams[teamIndex];

        // Get team name for the category
        let category = 'Other';
        switch (assignedTeam) {
          case AgenticTeamId.Marketing:
            category = 'Marketing';
            break;
          case AgenticTeamId.Research:
            category = 'Research';
            break;
          case AgenticTeamId.SoftwareDesign:
            category = 'Design';
            break;
          case AgenticTeamId.Sales:
            category = 'Sales';
            break;
          case AgenticTeamId.BusinessAnalysis:
            category = 'Admin';
            break;
        }

        // Check if planner context is available
        if (!this.plannerContext) {
          throw new Error('PlannerContext is not set. Call setPlannerContext before processing requests.');
        }

        // Create the task
        const task: Omit<Task, 'id'> = {
          projectId,
          title: `[${getTeamName(assignedTeam)}] ${breakdownItem.substring(0, 50)}...`,
          description: breakdownItem,
          category,
          status: 'Not Started',
          startDate: new Date(),
          dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
          assignedTo: [this.options.userId],
          priority: mapPriority(input.priority),
          dependencies: [],
          notes: `Assigned to ${getTeamName(assignedTeam)} team by PMO Agent`,
          createdBy: this.options.userId,
          createdAt: new Date()
        };

        // Use the createTask method from the planner context
        const taskId = await this.plannerContext.createTask(task);
        taskIds.push(taskId);

        // Add task ID to created tasks
        createdTasks.push({
          ...task,
          id: taskId
        });
      }

      // Update PMO record with task IDs
      await updatePMORecord(pmoRecordId, {
        taskIds: taskIds
      });

      // Update stream if enabled
      this._updateStream('complete', {
        pmoRecordId,
        projectId,
        taskIds,
        createdTasks
      }, 'PMO request processing complete!');

      // Return the result
      return {
        success: true,
        pmoRecordId,
        originalRequest: input.description,
        formalizedRequest: taskAnalysis.formalizedTask,
        taskAnalysis,
        pmoAssessment: taskAnalysis.formalizedTask, // Using formalized task as assessment
        assignedTeams: selectedTeams,
        createdTasks
      };
    } catch (error: any) {
      console.error('PMO Agent error:', error);
      return {
        success: false,
        pmoRecordId: '',
        originalRequest: input.description,
        formalizedRequest: '',
        taskAnalysis: {
          formalizedTask: '',
          breakdown: [],
          selectedTeams: [],
          teamSelectionRationale: '',
          recommendedResources: []
        },
        pmoAssessment: '', // Empty assessment for error case
        assignedTeams: [],
        createdTasks: [],
        error: error.message || 'An unknown error occurred'
      };
    }
  }

  /**
   * Analyze a task and determine requirements
   * @private
   * @param input - The PMO form input
   * @returns - The task analysis result
   */
  private async _analyzeTask(input: PMOFormInput): Promise<TaskAnalysisResult> {
    // Prepare the prompt for task analysis
    const prompt = `
You are a Project Management Office (PMO) Agent responsible for analyzing tasks and determining which of the five Agentic Teams should handle them.

THE FIVE AGENTIC TEAMS:
1. Marketing Team (Ag001): Specializes in marketing strategy, content creation, brand management, and market analysis.
2. Research Team (Ag002): Focuses on data collection, analysis, literature reviews, and producing research reports.
3. Software Design Team (Ag003): Handles software development, UI/UX design, coding, and technical implementation.
4. Sales Team (Ag004): Manages sales strategies, client relationships, proposal development, and revenue generation.
5. Business Analysis Team (Ag005): Specializes in business process analysis, requirements gathering, and strategic planning.

TASK DESCRIPTION:
${input.title}

DETAILED REQUIREMENTS:
${input.description}

${input.contextOptions.customContext ? `ADDITIONAL CONTEXT:\n${input.contextOptions.customContext}` : ''}

PRIORITY: ${input.priority}

Your job is to:
1. Formalize the task into a clear, structured description
2. Break down the task into logical sub-tasks
3. Determine which team(s) should handle this task and provide a detailed rationale
4. Recommend any resources or tools that might be helpful

Please provide your analysis in the following JSON format:
{
  "formalizedTask": "Clear, structured description of the task",
  "breakdown": [
    "Subtask 1 description",
    "Subtask 2 description",
    "Subtask 3 description"
  ],
  "selectedTeams": ["Ag001", "Ag003"],
  "teamSelectionRationale": "Detailed explanation of why these teams were selected",
  "recommendedResources": [
    "Resource 1",
    "Resource 2"
  ]
}

Ensure your response is ONLY the JSON object with no additional text.
`;

    // Process with simplePromptOptimizer
    const optimizerResult = await simplePromptOptimizer.optimizePrompt({
      originalPrompt: prompt,
      includeExplanation: false,
      modelOptions: {
        temperature: 0.5,
        maxTokens: 2000
      }
    });

    if (!optimizerResult.success) {
      throw new Error(`Failed to optimize prompt: ${optimizerResult.error}`);
    }

    const response = optimizerResult.optimizedPrompt;

    try {
      // Parse the JSON response
      const result = JSON.parse(response.trim());

      // Validate the result
      if (!result.formalizedTask || !Array.isArray(result.breakdown) || !Array.isArray(result.selectedTeams)) {
        throw new Error('Invalid task analysis result format');
      }

      // Convert team IDs to enum values if they're not already
      const selectedTeams = result.selectedTeams.map((team: string) => {
        if (Object.values(AgenticTeamId).includes(team as AgenticTeamId)) {
          return team as AgenticTeamId;
        }

        // Convert team name to ID if needed
        switch (team.toLowerCase()) {
          case 'marketing':
          case 'ag001':
            return AgenticTeamId.Marketing;
          case 'research':
          case 'ag002':
            return AgenticTeamId.Research;
          case 'software':
          case 'software design':
          case 'ag003':
            return AgenticTeamId.SoftwareDesign;
          case 'sales':
          case 'ag004':
            return AgenticTeamId.Sales;
          case 'business':
          case 'business analysis':
          case 'ag005':
            return AgenticTeamId.BusinessAnalysis;
          default:
            return AgenticTeamId.Research; // Default to Research if unknown
        }
      });

      return {
        formalizedTask: result.formalizedTask,
        breakdown: result.breakdown,
        selectedTeams,
        teamSelectionRationale: result.teamSelectionRationale,
        recommendedResources: result.recommendedResources || []
      };
    } catch (error) {
      console.error('Error parsing task analysis result:', error);
      throw new Error('Failed to analyze task. Please try again.');
    }
  }

  /**
   * Update the stream with the current status
   * @private
   * @param stage - The current processing stage
   * @param data - Any data to include in the update
   * @param message - A message describing the current status
   */
  private _updateStream(stage: PMOStreamUpdate['stage'], data?: any, message?: string): void {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({
        stage,
        data,
        message
      });
    }
  }
}

// Helper function to get team name from ID
function getTeamName(teamId: AgenticTeamId): string {
  switch (teamId) {
    case AgenticTeamId.Marketing:
      return 'Marketing';
    case AgenticTeamId.Research:
      return 'Research';
    case AgenticTeamId.SoftwareDesign:
      return 'Software Design';
    case AgenticTeamId.Sales:
      return 'Sales';
    case AgenticTeamId.BusinessAnalysis:
      return 'Business Analysis';
    default:
      return 'Unknown Team';
  }
}

// Helper function to map PMO priority to Task priority
function mapPriority(pmoPriority: string): 'Low' | 'Medium' | 'High' | 'Critical' {
  switch (pmoPriority) {
    case 'Low':
      return 'Low';
    case 'Medium':
      return 'Medium';
    case 'High':
      return 'High';
    case 'Critical':
      return 'Critical';
    default:
      return 'Medium';
  }
}

// Helper function to update PMO record
async function updatePMORecord(recordId: string, data: Partial<PMORecord>): Promise<void> {
  // Import here to avoid circular dependency
  const { updatePMORecord } = await import('../../firebase/pmo');
  return updatePMORecord(recordId, data);
}
