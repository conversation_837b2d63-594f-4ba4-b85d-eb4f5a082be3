/**
 * Create PMO Tasks Agent
 *
 * This agent automates the creation of tasks for PMO projects by:
 * 1. Using QueryDocumentsAgent to read Strategic Planning documents
 * 2. Using OpenAI o3 to analyze and derive tasks from strategic plans
 * 3. Using the createTasksTool to create the tasks in the system
 * 4. Integrating with Requirements Analysis documents for context
 */

import { QueryDocumentsAgent } from '../../components/Agents/QueryDocumentsAgent';
import { createTasksTool, CreateTasksInput, CreateTasksResponse } from '../tools/createTasksTool';
import { generateTasksListTool, GenerateTasksInput, GenerateTasksResponse, TaskItem } from '../tools/generateTasksListTool';
import { chartTool, ChartGenerationResult, CHART_TYPES } from '../tools';
import { Project, Task } from 'admin/planner/types';
import { processWithOpenAI } from '../tools/openai-ai';

export interface CreatePMOTasksAgentOptions {
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CreatePMOTasksStreamUpdate) => void;
}

export interface CreatePMOTasksStreamUpdate {
  stage: 'reading-strategic-plan' | 'reading-requirements' | 'analyzing-documents' | 'generating-tasks' | 'creating-tasks' | 'visualizing-tasks' | 'complete';
  data?: any;
  message?: string;
}

export interface CreatePMOTasksAgentResult {
  success: boolean;
  strategicPlanContent: string;
  requirementsContent?: string;
  analysis: string;
  reasoning: string;
  tasks: Task[];
  creationResults: CreateTasksResponse;
  taskListVisualization?: ChartGenerationResult;
  error?: string;
}

export class createPMOTasksAgent {
  private options: CreatePMOTasksAgentOptions;
  private queryDocumentsAgent: QueryDocumentsAgent;

  constructor(options: CreatePMOTasksAgentOptions = {}) {
    this.options = {
      includeExplanation: options.includeExplanation ?? true,
      streamResponse: options.streamResponse ?? false,
      onStreamUpdate: options.onStreamUpdate
    };

    // Initialize QueryDocumentsAgent
    this.queryDocumentsAgent = new QueryDocumentsAgent({
      maxResults: 10,
      defaultTemperature: 0.3,
      defaultMaxTokens: 4000,
      includeExplanation: true
    });
  }

  /**
   * Create tasks from strategic planning documents
   * @param projectId - ID of the project to create tasks for
   * @param project - Project details
   * @param strategicPlanDocumentId - ID of the strategic planning document
   * @param requirementsDocumentId - Optional ID of the requirements document
   * @returns - Result of the task creation process
   */
  async createTasksFromStrategicPlan(
    projectId: string,
    project: Project,
    strategicPlanDocumentId: string,
    requirementsDocumentId?: string
  ): Promise<CreatePMOTasksAgentResult> {
    try {
      if (!projectId || !project || !strategicPlanDocumentId) {
        throw new Error("Project ID, project details, and strategic plan document ID are required");
      }

      console.log("CreatePMOTasksAgent: Starting task creation from strategic plan");

      // Step 1: Read Strategic Planning Document
      this._streamUpdate('reading-strategic-plan', null, 'Reading strategic planning document...');
      console.log(`CreatePMOTasksAgent: Reading strategic plan document ${strategicPlanDocumentId}`);

      const strategicPlanQuery = `
Extract the complete strategic plan content including:
- Strategic objectives and goals
- Implementation timeline
- Resource requirements
- Key deliverables and milestones
- Success metrics and KPIs
- Risk assessment and mitigation strategies
- Team responsibilities and assignments
`;

      const strategicPlanResult = await this.queryDocumentsAgent.queryDocuments({
        query: strategicPlanQuery,
        fileIds: [strategicPlanDocumentId],
        maxResults: 10,
        userId: project.owner
      });

      if (!strategicPlanResult.success || !strategicPlanResult.results.length) {
        throw new Error(`Failed to read strategic plan document: ${strategicPlanResult.error}`);
      }

      const strategicPlanContent = strategicPlanResult.results.join('\n\n');
      console.log("CreatePMOTasksAgent: Strategic plan content retrieved successfully");

      // Step 2: Read Requirements Document (if provided)
      let requirementsContent = '';
      if (requirementsDocumentId) {
        this._streamUpdate('reading-requirements', null, 'Reading requirements document...');
        console.log(`CreatePMOTasksAgent: Reading requirements document ${requirementsDocumentId}`);

        const requirementsQuery = `
Extract the complete requirements analysis including:
- Functional requirements
- Non-functional requirements
- Business requirements
- Technical specifications
- Constraints and assumptions
- Acceptance criteria
`;

        const requirementsResult = await this.queryDocumentsAgent.queryDocuments({
          query: requirementsQuery,
          fileIds: [requirementsDocumentId],
          maxResults: 10,
          userId: project.owner
        });

        if (requirementsResult.success && requirementsResult.results.length) {
          requirementsContent = requirementsResult.results.join('\n\n');
          console.log("CreatePMOTasksAgent: Requirements content retrieved successfully");
        }
      }

      // Step 3: Analyze documents and derive tasks using OpenAI o3
      this._streamUpdate('analyzing-documents', {
        strategicPlanContent: strategicPlanContent.substring(0, 500) + '...',
        requirementsContent: requirementsContent.substring(0, 500) + '...'
      }, 'Analyzing documents with OpenAI o3...');

      console.log("CreatePMOTasksAgent: Analyzing documents with OpenAI o3");

      const analysisPrompt = `
You are a senior project management expert tasked with deriving specific, actionable tasks from strategic planning documents.

PROJECT INFORMATION:
- Project Name: ${project.name}
- Project Description: ${project.description}
- Start Date: ${project.startDate.toISOString().split('T')[0]}
- End Date: ${project.endDate.toISOString().split('T')[0]}
- Categories: ${project.categories.join(', ')}

STRATEGIC PLAN CONTENT:
${strategicPlanContent}

${requirementsContent ? `
REQUIREMENTS ANALYSIS:
${requirementsContent}
` : ''}

TASK DERIVATION INSTRUCTIONS:
1. Analyze the strategic plan and identify all actionable items, deliverables, and milestones
2. Break down high-level objectives into specific, measurable tasks
3. Consider the requirements analysis to ensure technical and business needs are addressed
4. Create a chronological sequence of tasks that aligns with the project timeline
5. Assign realistic priorities based on dependencies and business impact
6. Ensure each task has clear acceptance criteria and deliverables

For each task, provide:
- Title (concise and action-oriented)
- Detailed description with acceptance criteria
- Priority (Critical, High, Medium, Low)
- Estimated start and due dates within the project timeframe
- Dependencies on other tasks
- Category (from project categories)
- Notes with additional context or requirements

Return your analysis and task list in the following JSON format:
{
  "analysis": "Your detailed analysis of the strategic plan and how tasks were derived",
  "reasoning": "Your reasoning for the task breakdown and prioritization",
  "tasks": [
    {
      "title": "Task Title",
      "description": "Detailed task description with acceptance criteria",
      "priority": "High",
      "startDate": "YYYY-MM-DD",
      "dueDate": "YYYY-MM-DD",
      "dependencies": ["Task Title 1", "Task Title 2"],
      "category": "Category Name",
      "notes": "Additional notes and context"
    }
  ]
}
`;

      const analysisResult = await processWithOpenAI({
        prompt: analysisPrompt,
        model: 'o3-2025-04-16',
        modelOptions: {
          temperature: 0.2,
          maxTokens: 8000,
        },
      });

      // Parse the analysis result
      const jsonMatch = analysisResult.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in OpenAI o3 analysis response');
      }

      const parsedAnalysis = JSON.parse(jsonMatch[0]);
      console.log(`CreatePMOTasksAgent: Analysis complete, derived ${parsedAnalysis.tasks.length} tasks`);

      // Step 4: Create tasks in the system using createTasksTool
      this._streamUpdate('creating-tasks', {
        analysis: parsedAnalysis.analysis,
        reasoning: parsedAnalysis.reasoning,
        tasks: parsedAnalysis.tasks
      }, 'Creating tasks in the system...');

      console.log("CreatePMOTasksAgent: Creating tasks in the system");

      const createTasksInput: CreateTasksInput = {
        projectId,
        tasks: parsedAnalysis.tasks
      };

      const createTasksResult = await createTasksTool.execute(createTasksInput);
      console.log(`CreatePMOTasksAgent: Created ${createTasksResult.results.filter(r => r.success).length} tasks successfully`);

      // Step 5: Generate a table visualization of the tasks
      this._streamUpdate('visualizing-tasks', null, 'Generating task list visualization...');
      console.log("CreatePMOTasksAgent: Generating task list visualization");

      const tasks = this._mapTaskItemsToTasks(parsedAnalysis.tasks, projectId);
      const taskListVisualization = await this._generateTaskListVisualization(project.name, tasks);

      console.log("CreatePMOTasksAgent: Task list visualization generated");

      // Step 6: Return the complete result
      const result: CreatePMOTasksAgentResult = {
        success: true,
        strategicPlanContent,
        requirementsContent: requirementsContent || undefined,
        analysis: parsedAnalysis.analysis,
        reasoning: parsedAnalysis.reasoning,
        tasks,
        creationResults: createTasksResult,
        taskListVisualization
      };

      this._streamUpdate('complete', result, 'PMO task creation complete!');
      return result;

    } catch (error) {
      console.error("CreatePMOTasksAgent: Error creating tasks:", error);

      return {
        success: false,
        strategicPlanContent: '',
        analysis: '',
        reasoning: '',
        tasks: [],
        creationResults: {
          results: [],
          summary: 'PMO task creation failed'
        },
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Stream update helper
   * @private
   */
  private _streamUpdate(stage: CreatePMOTasksStreamUpdate['stage'], data?: any, message?: string): void {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({ stage, data, message });
    }
  }

  /**
   * Map TaskItems to Tasks
   * @private
   */
  private _mapTaskItemsToTasks(taskItems: TaskItem[], projectId: string): Task[] {
    return taskItems.map((task, index) => {
      const mappedTask: Task = {
        id: `pmo-task-${Date.now()}-${index}`,
        title: task.title,
        description: task.description,
        projectId,
        category: task.category,
        status: 'Not Started',
        startDate: new Date(task.startDate),
        dueDate: new Date(task.dueDate),
        assignedTo: ['<EMAIL>'], // Assign to Admin User
        priority: task.priority,
        dependencies: task.dependencies || [],
        notes: task.notes,
        createdBy: 'pmo-agent',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return mappedTask;
    });
  }

  /**
   * Generate task list visualization
   * @private
   */
  private async _generateTaskListVisualization(projectName: string, tasks: Task[]): Promise<ChartGenerationResult> {
    const chartPrompt = `
Create a comprehensive task management table for the project "${projectName}".

TASKS DATA:
${JSON.stringify(tasks, null, 2)}

The table should have the following columns:
- ID
- Title
- Priority (color-coded: Critical=Red, High=Orange, Medium=Yellow, Low=Green)
- Status (color-coded: Complete=Green, In Progress=Yellow, Reviewed=Blue, Not Started=Gray)
- Start Date
- Due Date
- Category
- Dependencies

Sort the tasks by start date.
Include a detailed explanation of the PMO task breakdown and strategic alignment.
`;

    return await chartTool.generateChart({
      prompt: chartPrompt,
      chartType: CHART_TYPES.TABLE,
      model: "o3-2025-04-16",
      provider: "openai"
    });
  }
}
