import { simplePromptOptimizer } from '../../tools/simplePromptOptimizer'; // Ensure this path is correct and the module is compliant
import {
  PMOAgentOptions,
  PMOFormInput,
  PMOStreamUpdate,
  AgenticTeamId,
} from './PMOInterfaces'; // Ensure this path and its contents are correct
import { v4 as uuidv4 } from 'uuid';
import { addAgentOutput } from '../../../lib/firebase/agentOutputs'; // Ensure this path is correct
import { processPMODocument } from '../../pmo/processPMODocument'; // Ensure this path is correct

// --- BEGIN INTERFACE DEFINITIONS ---
interface IQueryDocumentsAgent {
  queryDocuments(params: {
    query: string;
    fileIds: string[];
    maxResults: number;
    userId?: string;
  }): Promise<{ success: boolean; results: string[]; chunks?: any[]; error?: string }>;

  queryDocumentsByCategory(params: {
    query: string;
    categoryIds: string[];
    maxResults: number;
    userId?: string;
  }): Promise<{ success: boolean; results: string[]; chunks?: any[]; error?: string }>;
}

interface IStrategicDirectorAgent {
  analyzeTask(params: {
    title: string;
    description: string;
    context: string;
    taskType: string;
  }): Promise<{
    success: boolean;
    recommendedTeams?: AgenticTeamId[];
    rationale?: string;
    error?: string;
  }>;
}

interface IDateTimeTool {
  getCurrentDateTime(): Promise<{
    success: boolean;
    formattedDateTime?: string;
    error?: string;
  }>;
}

interface IPDFGenerator {
  generatePDF(params: {
    title: string;
    content: string;
    fileName: string;
  }): Promise<{ success: boolean; fileUrl?: string; error?: string }>;
}

// Interface for a direct LLM call
interface LlmResponse {
    success: boolean;
    generatedText?: string;
    error?: string;
}

interface LlmService {
    generateText(params: {
        prompt: string;
        modelOptions: {
            temperature: number;
            maxTokens: number;
            model?: string; // modelName from PMOFormInput
        };
    }): Promise<LlmResponse>;
}
// --- END INTERFACE DEFINITIONS ---


export class PMOAssessmentAgent {
  private options: PMOAgentOptions;
  private queryDocumentsAgent: IQueryDocumentsAgent | null;
  private strategicDirectorAgent: IStrategicDirectorAgent | null;
  private dateTimeTool: IDateTimeTool | null;
  private pdfGenerator: IPDFGenerator | null;
  private llmService: LlmService; // For direct LLM calls

  constructor(options: PMOAgentOptions, llmService?: LlmService) {
    this.options = {
      userId: options.userId,
      includeExplanation: options.includeExplanation || false,
      streamResponse: options.streamResponse || false,
      onStreamUpdate: options.onStreamUpdate
    };

    if (!this.options.userId) {
        throw new Error("PMOAssessmentAgent requires a userId in options.");
    }

    this.llmService = llmService || simplePromptOptimizer as any;

    this.queryDocumentsAgent = null;
    this.strategicDirectorAgent = null;
    this.dateTimeTool = null;
    this.pdfGenerator = null;
  }

  public setQueryDocumentsAgent(agent: IQueryDocumentsAgent): void {
    this.queryDocumentsAgent = agent;
  }

  public setStrategicDirectorAgent(agent: IStrategicDirectorAgent): void {
    this.strategicDirectorAgent = agent;
  }

  public setDateTimeTool(tool: IDateTimeTool): void {
    this.dateTimeTool = tool;
  }

  public setPDFGenerator(generator: IPDFGenerator): void {
    this.pdfGenerator = generator;
  }

  public async generateRequirementsSpecificationDirectly(
    input: PMOFormInput,
    assessment: string,
    selectedTeams: AgenticTeamId[],
    contextChunks: any[] = []
  ): Promise<string> {
    return this._generateRequirementsSpecification(input, assessment, selectedTeams, contextChunks);
  }

  public async generatePDF(title: string, content: string): Promise<{ success: boolean; fileUrl?: string; error?: string; }> {
    return this._generatePDF(title, content);
  }

  public async saveAgentOutput(
    title: string,
    content: string,
    fileUrl: string,
    selectedTeams: AgenticTeamId[],
    priority: string
  ): Promise<{ success: boolean; id?: string; error?: string; }> {
    return this._saveAgentOutput(title, content, fileUrl, selectedTeams, priority);
  }

  public hasPDFGenerator(): boolean {
    return !!this.pdfGenerator && typeof this.pdfGenerator.generatePDF === 'function';
  }

  public async generateAssessment(input: PMOFormInput): Promise<{
    pmoAssessment: string;
    selectedTeams: AgenticTeamId[];
    requirementsDocumentId?: string;
    teamSelectionRationale?: string;
  }> {
    try {
      if (!this.options.userId) {
        throw new Error("UserID is missing from PMOAssessmentAgent options.");
      }

      if (!this.llmService || typeof this.llmService.generateText !== 'function') {
        throw new Error("LLMService with generateText method is not configured for PMOAssessmentAgent.");
      }

      if (!input.contextOptions.customContext &&
          (!input.contextOptions.fileIds || input.contextOptions.fileIds.length === 0) &&
          (!input.contextOptions.categoryIds || input.contextOptions.categoryIds.length === 0)) {
        this._updateStream('error', { message: 'No context provided.' }, 'Context is required for assessment.');
        throw new Error('No context provided. Please provide custom context, select files, or select categories to proceed.');
      }

      this._updateStream('analyzing-task', { inputTitle: input.title }, 'Analyzing task and determining requirements...');

      let contextData = '';
      let contextChunks: any[] = [];

      if (input.contextOptions.customContext) {
        this._updateStream('context-retrieval', { type: 'custom' }, 'Using provided custom context.');
        contextData = input.contextOptions.customContext;
        // Create a synthetic chunk for custom context to be used later
        contextChunks = [{
            content: input.contextOptions.customContext,
            text: input.contextOptions.customContext,
            metadata: { fileName: 'Custom Context', source: 'User Input', relevance: 1.0 }
        }];
      } else if (input.contextOptions.fileIds && input.contextOptions.fileIds.length > 0) {
        this._updateStream('context-retrieval', { type: 'files', ids: input.contextOptions.fileIds }, 'Querying documents by file IDs...');
        if (!this.queryDocumentsAgent) {
          throw new Error('QueryDocumentsAgent is not set. Call setQueryDocumentsAgent before processing requests.');
        }
        if (typeof this.queryDocumentsAgent.queryDocuments !== 'function') {
            throw new TypeError('QueryDocumentsAgent instance is missing the required queryDocuments method.');
        }
        const queryResult = await this.queryDocumentsAgent.queryDocuments({
          query: input.description,
          fileIds: input.contextOptions.fileIds,
          maxResults: 5,
          userId: this.options.userId
        });
        if (queryResult.success) {
          contextData = queryResult.results.join('\n\n').trim();
          contextChunks = queryResult.chunks || [];
          console.log(`PMOAssessmentAgent: Retrieved ${contextChunks.length} context chunks`);
          contextChunks.forEach((chunk, index) => {
            const source = chunk.metadata?.fileName || chunk.metadata?.source || 'N/A';
            const relevance = chunk.metadata?.relevance ? ` (Relevance: ${(chunk.metadata.relevance * 100).toFixed(1)}%)` : '';
            console.log(`PMOAssessmentAgent: Context Chunk ${index + 1} - [Section: ${source}${relevance}]`);
            console.log(`PMOAssessmentAgent: Content preview: ${(chunk.content || chunk.text || 'No content').substring(0, 100)}...`);
          });
          if (!contextData && queryResult.results.length > 0) {
             contextData = queryResult.results.filter(r => r && r.trim()).join('\n\n').trim();
          }
          if (!contextData) {
             console.warn(`PMOAssessmentAgent: No relevant context found from files.`);
             contextData = '';
          } else {
             console.log(`PMOAssessmentAgent: Context data preview: ${contextData.substring(0, 200)}...`);
          }
        } else {
          throw new Error(`Failed to retrieve context from files: ${queryResult.error || 'Query failure'}`);
        }
      } else if (input.contextOptions.categoryIds && input.contextOptions.categoryIds.length > 0) {
        this._updateStream('context-retrieval', { type: 'categories', ids: input.contextOptions.categoryIds }, 'Querying documents by category IDs...');
        if (!this.queryDocumentsAgent) {
          throw new Error('QueryDocumentsAgent is not set.');
        }
        if (typeof this.queryDocumentsAgent.queryDocumentsByCategory !== 'function') {
            throw new TypeError('QueryDocumentsAgent instance is missing queryDocumentsByCategory method.');
        }
        const queryResult = await this.queryDocumentsAgent.queryDocumentsByCategory({
          query: input.description,
          categoryIds: input.contextOptions.categoryIds,
          maxResults: 5,
          userId: this.options.userId
        });
        if (queryResult.success) {
          contextData = queryResult.results.join('\n\n').trim();
          contextChunks = queryResult.chunks || [];
           if (!contextData && queryResult.results.length > 0) {
             contextData = queryResult.results.filter(r => r && r.trim()).join('\n\n').trim();
          }
          if (!contextData) {
             console.warn(`PMOAssessmentAgent: No relevant context found from categories.`);
             contextData = '';
          }
        } else {
          throw new Error(`Failed to retrieve context from categories: ${queryResult.error || 'Query failure'}`);
        }
      }

      this._updateStream('generating-assessment', { contextDataPreview: contextData ? contextData.substring(0,100) + '...' : 'N/A', contextChunksCount: contextChunks.length }, 'Generating PMO assessment...');
      const assessment = await this._generatePMOAssessment(input, contextData);

      this._updateStream('delegating-to-strategic-director', { assessmentPreview: assessment.substring(0,100) + '...' }, 'Delegating to Strategic Director for team selection...');
      let selectedTeams: AgenticTeamId[] = [];
      let teamSelectionRationale: string | undefined;

      if (this.strategicDirectorAgent) {
        if (typeof this.strategicDirectorAgent.analyzeTask !== 'function') {
            throw new TypeError('StrategicDirectorAgent instance is missing analyzeTask method.');
        }
        try {
          const directorResult = await this.strategicDirectorAgent.analyzeTask({
            title: input.title,
            description: input.description,
            context: assessment,
            taskType: 'PMO_Assessment'
          });
          if (directorResult.success && directorResult.recommendedTeams && directorResult.recommendedTeams.length > 0) {
            selectedTeams = directorResult.recommendedTeams;
            teamSelectionRationale = directorResult.rationale || 'Teams selected by Strategic Director.';
          } else {
            console.warn('Strategic Director analysis failed or no teams recommended. Falling back.', directorResult.error);
            const fallbackResult = await this._fallbackTeamSelection(input, assessment);
            selectedTeams = fallbackResult.teams;
            teamSelectionRationale = fallbackResult.rationale;
          }
        } catch (error: any) {
          console.error('Error with Strategic Director, falling back:', error);
          const fallbackResult = await this._fallbackTeamSelection(input, assessment);
          selectedTeams = fallbackResult.teams;
          teamSelectionRationale = `Fallback due to SD error: ${error.message || 'Unknown'}`;
        }
      } else {
        console.warn('StrategicDirectorAgent not set. Using fallback team selection.');
        const fallbackResult = await this._fallbackTeamSelection(input, assessment);
        selectedTeams = fallbackResult.teams;
        teamSelectionRationale = fallbackResult.rationale;
      }

      if(selectedTeams.length === 0) {
        console.warn('No teams selected. Defaulting to Research team.');
        selectedTeams = [AgenticTeamId.Research];
        teamSelectionRationale = (teamSelectionRationale ? teamSelectionRationale + " " : "") + "Defaulted to Research team.";
      }

      this._updateStream('generating-requirements-document', { assessmentPreview: assessment.substring(0,100) + '...', selectedTeams }, 'Generating requirements specification document...');
      let requirementsDocumentId: string | undefined;
      try {
        const requirementsSpec = await this._generateRequirementsSpecification(input, assessment, selectedTeams, contextChunks);

        if (!this.pdfGenerator) {
            console.warn("PDFGenerator not set. Skipping PDF generation.");
        } else {
             if (typeof this.pdfGenerator.generatePDF !== 'function') {
                throw new TypeError('PDFGenerator instance is missing generatePDF method.');
            }
            const pdfResult = await this._generatePDF(input.title, requirementsSpec);
            if (pdfResult.success && pdfResult.fileUrl) {
              const outputResult = await this._saveAgentOutput(
                input.title,
                requirementsSpec,
                pdfResult.fileUrl,
                selectedTeams,
                input.priority || 'Medium'
              );
              requirementsDocumentId = outputResult.id;
            } else {
              console.error(`Failed to generate PDF: ${pdfResult.error}.`);
            }
        }
      } catch (error: any) {
        console.error('Error in requirements document generation/saving:', error);
      }

      this._updateStream('complete', { assessmentPreview: assessment.substring(0,100) + '...', selectedTeams, requirementsDocumentId, teamSelectionRationale }, 'PMO assessment generation complete!');
      return { pmoAssessment: assessment, selectedTeams, requirementsDocumentId, teamSelectionRationale };

    } catch (error: any) {
      console.error('PMO Assessment Agent top-level error:', error);
      this._updateStream('error', { message: error.message, stack: error.stack }, 'PMO assessment generation failed at top level.');
      throw error;
    }
  }

  private async _generatePMOAssessment(input: PMOFormInput, contextData: string): Promise<string> {
    const prompt = `
You are a Project Management Office (PMO) Agent responsible for analyzing project requests and generating formal assessments.
The user has provided the following (potentially already optimized) request description. Your task is to use this, along with the provided context data, to generate a comprehensive PMO assessment.

TASK INFORMATION:
Title: ${input.title}
Description (User-Provided): ${input.description}
Priority: ${input.priority || 'Not Specified'}
Category: ${input.category || 'Unknown'}
Source File: ${input.sourceFile || 'None'}
File Name: ${input.fileName || 'None'}
Source Data: ${contextData.trim() || 'None'}

CONTEXT DATA (IMPORTANT - USE THIS INFORMATION IN YOUR ASSESSMENT):
${contextData.trim() || 'No additional context provided.'}

Your task is to:
1. Analyze the user-provided description and context data.
2. Generate a formal PMO assessment that includes:
   - A clear problem statement or project goal based on the input.
   - Key objectives.
   - High-level requirements or deliverables.
   - Potential challenges or risks.
   - Suggested next steps or considerations.
3. The assessment should be comprehensive, professional, and actionable.
4. Based on your analysis, suggest the most suitable Agentic Team(s) (e.g., Marketing, Research, SoftwareDesign, Sales, BusinessAnalysis) and provide a brief rationale for your suggestion. This suggestion will be reviewed.

Please provide a well-structured PMO assessment. Output the assessment directly.
`;

    const llmResult = await this.llmService.generateText({
      prompt: prompt,
      modelOptions: {
        temperature: 0.5,
        maxTokens: 4000,
        model: input.modelName ?? undefined
      }
    });

    if (!llmResult.success || !llmResult.generatedText) {
      throw new Error(`Failed to generate PMO assessment from LLM: ${llmResult.error || 'Empty response'}`);
    }
    return llmResult.generatedText.trim();
  }

  private async _fallbackTeamSelection(input: PMOFormInput, assessment: string): Promise<{ teams: AgenticTeamId[]; rationale: string; }> {
    const prompt = `
You are PMO team delegation agent helping to select appropriate Agentic Teams for a task based on its description and a PMO assessment.
The user provided this description: "${input.description}"

THE FIVE AGENTIC TEAMS (IDs and descriptions):
1. Marketing (Ag001): Specializes in marketing strategy, content creation, brand management, and market analysis.
2. Research (Ag002): Focuses on data collection, analysis, literature reviews, and producing research reports.
3. SoftwareDesign (Ag003): Handles software development, UI/UX design, coding, and technical implementation.
4. Sales (Ag004): Manages sales strategies, client relationships, proposal development, and revenue generation.
5. BusinessAnalysis (Ag005): Specializes in business process analysis, requirements gathering, and strategic planning.

TASK INFORMATION:
Title: ${input.title}
User-Provided Description: ${input.description}
Priority: ${input.priority || 'Not Specified'}
Category: ${input.category || 'Unknown'}

PMO ASSESSMENT SUMMARY (first 1500 chars):
${assessment.substring(0, 1500)}

Based on the task information and PMO assessment, select the MOST appropriate team(s) (usually one, sometimes two if strong overlap).
Provide your selection in strict JSON format: { "selectedTeams": ["Ag00X", ...], "rationale": "Brief explanation for the choice(s)." }
Valid team IDs are: "Ag001", "Ag002", "Ag003", "Ag004", "Ag005".
Ensure your response is ONLY the JSON object.
`;

    const llmResult = await this.llmService.generateText({
      prompt: prompt,
      modelOptions: {
        temperature: 0.3,
        maxTokens: 500,
        model: input.modelName ?? undefined
      }
    });

    if (!llmResult.success || !llmResult.generatedText) {
      console.error('Fallback team selection LLM call failed:', llmResult.error);
      return { teams: [AgenticTeamId.Research], rationale: 'Defaulted: Fallback AI for team selection failed.' };
    }

    try {
      const resultJson = llmResult.generatedText.trim();
      const match = resultJson.match(/{[\s\S]*}/);
      if (!match) throw new Error("No JSON object in fallback team selection LLM response: " + resultJson);

      const parsedResult = JSON.parse(match[0]);
      if (!Array.isArray(parsedResult.selectedTeams) || typeof parsedResult.rationale !== 'string') {
        throw new Error('Invalid JSON structure from fallback team selection LLM. Parsed: ' + JSON.stringify(parsedResult));
      }

      const teams: AgenticTeamId[] = parsedResult.selectedTeams
        .map((id: string) => {
          const teamEnumValue = Object.values(AgenticTeamId).find(val => val === id);
          if (!teamEnumValue) {
            console.warn(`Invalid team ID "${id}" in fallback selection. Ignoring.`);
            return undefined;
          }
          return teamEnumValue;
        })
        .filter((t?: AgenticTeamId): t is AgenticTeamId => t !== undefined);

      if (teams.length === 0) {
        return { teams: [AgenticTeamId.Research], rationale: 'Defaulted: Fallback selection parsing led to no valid teams.' };
      }
      return { teams, rationale: parsedResult.rationale || 'Rationale not provided by fallback.' };

    } catch (error: any) {
      console.error('Error parsing fallback team selection LLM result:', error, "Raw LLM response:", llmResult.generatedText);
      return { teams: [AgenticTeamId.Research], rationale: `Defaulted: Error parsing fallback AI response: ${error.message}` };
    }
  }

  private async _generateRequirementsSpecification(
    input: PMOFormInput, assessment: string, selectedTeams: AgenticTeamId[], contextChunks: any[] = []
  ): Promise<string> {
    console.log(`PMOAssessmentAgent: Received ${contextChunks.length} context chunks for requirements specification`);

    if (!contextChunks || contextChunks.length === 0) {
      console.log(`PMOAssessmentAgent: No context chunks provided, creating a default chunk from input description`);
      contextChunks = [{
        content: input.description,
        text: input.description,
        metadata: {
          fileName: 'Project Description',
          source: 'User Input',
          relevance: 1.0
        }
      }];
    }

    contextChunks.forEach((chunk, index) => {
      console.log(`PMOAssessmentAgent: Context Chunk ${index + 1} raw data for req spec:`, JSON.stringify({
        hasContent: !!chunk.content,
        hasText: !!chunk.text,
        metadata: chunk.metadata || 'No metadata',
        contentPreview: (chunk.content || chunk.text || 'No content').substring(0, 100) + '...'
      }));
    });

    const processedChunks = contextChunks.map(chunk => {
      const processedChunk = { ...chunk };
      if (!processedChunk.content && !processedChunk.text) {
        console.warn(`PMOAssessmentAgent: Req Spec - Found chunk without content or text, creating empty chunk`);
        processedChunk.content = 'No content available';
        processedChunk.text = 'No content available';
      } else if (!processedChunk.content) {
        processedChunk.content = processedChunk.text;
      } else if (!processedChunk.text) {
        processedChunk.text = processedChunk.content;
      }

      if (!processedChunk.metadata) {
        console.warn(`PMOAssessmentAgent: Req Spec - Found chunk without metadata, creating default metadata`);
        processedChunk.metadata = {
          fileName: 'Unknown Source',
          source: 'Unknown',
          relevance: 0.5
        };
      }
      return processedChunk;
    });

    const contextDataForPrompt = processedChunks.map(chunk => {
      const source = chunk.metadata?.fileName || chunk.metadata?.source || 'N/A';
      const relevance = chunk.metadata?.relevance ? ` (Relevance: ${(chunk.metadata.relevance * 100).toFixed(1)}%)` : '';
      const fullContent = chunk.content || chunk.text || 'No content';
      return `[Section: ${source}${relevance}]\n${fullContent}`;
    }).join('\n\n').trim() || 'No additional context provided.';

    console.log(`PMOAssessmentAgent: Generated context data for requirements specification prompt (${contextDataForPrompt.length} chars)`);
    if (contextDataForPrompt.length > 0) {
      console.log(`PMOAssessmentAgent: Context data prompt preview: ${contextDataForPrompt.substring(0, 200)}...`);
    }

    let dateTimeStr = new Date().toISOString();
    if (this.dateTimeTool) {
        if (typeof this.dateTimeTool.getCurrentDateTime !== 'function') {
            console.warn('DateTimeTool instance is missing getCurrentDateTime method.');
        } else {
            try {
                const dateTimeResult = await this.dateTimeTool.getCurrentDateTime();
                if (dateTimeResult.success && dateTimeResult.formattedDateTime) {
                  dateTimeStr = dateTimeResult.formattedDateTime;
                }
            } catch (error: any) {
                console.error('Error getting date from DateTimeTool:', error.message);
            }
        }
    }

    const teamNames = selectedTeams.map(teamId => {
        const enumKey = Object.keys(AgenticTeamId).find(key => (AgenticTeamId as any)[key] === teamId);
        return enumKey || teamId.toString();
    }).join(', ');

    const prompt = `
You are a Project Management Office (PMO) Agent tasked with creating a formal Requirements Specification Document in Markdown format.
The user's core request description is: "${input.description}"

DOCUMENT TITLE: Requirements Specification - ${input.title}
DATE: ${dateTimeStr}
PROJECT TITLE: ${input.title}
PRIORITY: ${input.priority || 'Not Specified'}
ASSIGNED TEAMS (Recommended): ${teamNames}
REQUESTED BY (User ID): ${this.options.userId}

SUMMARIZE THE CONTEXT DATA (IMPORTANT - USE THIS INFORMATION IN YOUR ASSESSMENT):
${contextDataForPrompt}

1.  **EXECUTIVE SUMMARY:**
    (Provide a brief overview of the project, its purpose, and expected outcomes, derived from the PMO assessment, the user's request description, **and the complete document content provided in section 10. Be sure to incorporate technical details from the document analysis.**)

2.  **PROJECT OVERVIEW:**
    *   **Background:** (Detail the context and reasons for initiating this project/task. Use information from the PMO assessment, the user's request description: "${input.description}", **and the full document content in section 10. Include specific technical details from the document analysis.**)
    *   **Objectives:** (List the key goals this project aims to achieve. Be specific and measurable if possible, drawing from all available information including the PMO assessment, user request description, **and the complete document content in section 10.**)
    *   **Scope:**
        *   **In Scope:** (Clearly define what tasks, deliverables, and features are included in this project, informed by all inputs including **the full document content in section 10. Include specific technical requirements from the document analysis.**)
        *   **Out of Scope:** (Explicitly state what is not part of this project to avoid ambiguity, informed by all inputs including **the full document content in section 10.**)

3.  **STAKEHOLDERS:**
    *   **Requestor:** ${this.options.userId}
    *   **Recommended Assigned Team(s):** ${teamNames}
    *   **Other Key Stakeholders (if inferable or known from any source, including the full document content in section 10):** (e.g., End Users, Department Heads, Technical Teams)

4.  **FUNCTIONAL REQUIREMENTS:**
    (Describe the specific actions or functionalities the final product or solution must perform, based on the user's request, the PMO assessment, **and the complete document content in section 10. Extract and include all technical specifications and requirements from the document analysis.** Use clear, numbered or bulleted lists.)

5.  **NON-FUNCTIONAL REQUIREMENTS:**
    (Specify the quality attributes of the solution, such as performance, security, usability. Use clear, numbered or bulleted lists. **Extract and include all relevant technical specifications from the document content in section 10, especially those related to performance metrics, accuracy requirements, and technical constraints.**)

6.  **CONSTRAINTS AND ASSUMPTIONS:**
    *   **Constraints:** (List any limitations or restrictions identified from the request, assessment, **or the full document content in section 10. Include any technical limitations mentioned in the document analysis.**)
    *   **Assumptions:** (List factors assumed to be true, **including any technical assumptions that can be derived from the document content in section 10.**)

7.  **RISKS AND MITIGATIONS (PRELIMINARY):**
    (Identify potential risks. Suggest initial mitigation strategies. Consider risks highlighted in the assessment or implied by the **document content in section 10. Include any technical risks that might be inferred from the document analysis.**)
    *   Risk 1: ... Mitigation: ...

8.  **TIMELINE AND MILESTONES (HIGH-LEVEL):**
    (Outline initial thoughts on duration or key milestones. If not yet defined, state "To be determined". **Reference any timeline information or development phases that might be mentioned in the document content in section 10.**)

9.  **PMO ASSESSMENT SUMMARY:**
${assessment}

10. **SUPPORTING CONTEXT INFORMATION (FROM DOCUMENTS/CATEGORIES):**
${processedChunks.length > 0 ?
  processedChunks.map(chunk => {
    const source = chunk.metadata?.fileName || chunk.metadata?.source || 'N/A';
    const relevance = chunk.metadata?.relevance ? ` (Relevance: ${(chunk.metadata.relevance * 100).toFixed(1)}%)` : '';
    const fullContent = chunk.content || chunk.text || 'No content';
    // Indent all lines of fullContent to align them under the list item
    const indentedContent = fullContent.split('\n').map((line: string) => `        ${line.trim()}`).join('\n');
    return `    *   **[Section: ${source}${relevance}]**\n${indentedContent}`;
  }).join('\n\n')
  : '    No specific context chunks were extracted for this request.'}

11. **APPROVAL SECTION (Placeholder for Sign-off):**
    *   Project Manager: _________________________ Date: ____________
    *   Lead(s) of Assigned Team(s): _________________________ Date: ____________

---
Instructions:
- Use the PMO Assessment (section 9), the user's original request description ("${input.description}"), **and critically, the FULL details provided in 'SUPPORTING CONTEXT INFORMATION (FROM DOCUMENTS/CATEGORIES)' (section 10)** to fill out sections 1-8 comprehensively.
- **Your primary goal is to synthesize all these pieces of information into a coherent and detailed requirements specification.**
- **IMPORTANT: The 'SUPPORTING CONTEXT INFORMATION' section contains the complete extracted document content with relevance scores. You MUST incorporate this information in your requirements specification.**
- Focus on extracting, inferring, and structuring requirements clearly, **integrating all sources of information, especially the document content in section 10.**
- Pay special attention to technical details, specifications, and requirements found in the document content.
- If information for a specific subsection is not available from any source, state "To be determined".
- Ensure professional language and Markdown formatting.
`;

    console.log(`PMOAssessmentAgent: Generating requirements specification with prompt length ${prompt.length} chars`);

    const llmResult = await this.llmService.generateText({
      prompt: prompt,
      modelOptions: {
        temperature: 0.4,
        maxTokens: 6000,
        model: input.modelName ?? undefined
      }
    });

    if (!llmResult.success || !llmResult.generatedText) {
      throw new Error(`Failed to generate requirements specification from LLM: ${llmResult.error || 'Empty response'}`);
    }
    return llmResult.generatedText.trim();
  }

  private async _generatePDF(title: string, content: string): Promise<{ success: boolean; fileUrl?: string; error?: string; }> {
    if (!this.pdfGenerator) {
      console.warn('PDFGenerator is not set. PDF generation skipped.');
      return { success: false, error: 'PDF generator is not set.' };
    }
    if (typeof this.pdfGenerator.generatePDF !== 'function') {
        console.error('PDFGenerator instance is missing generatePDF method.');
        return { success: false, error: 'PDFGenerator instance is missing generatePDF method.' };
    }
    try {
      const safeFileName = title.replace(/[^a-zA-Z0-9_.-]/g, '_');
      return await this.pdfGenerator.generatePDF({
        title: `PMO_ReqSpec_${safeFileName}`,
        content: content,
        fileName: `PMO_ReqSpec_${safeFileName}_${Date.now()}.pdf`
      });
    } catch (error: any) {
      console.error('Error generating PDF:', error);
      return { success: false, error: error.message || 'PDF generation failed' };
    }
  }

  private async _saveAgentOutput(
    title: string, content: string, fileUrl: string, selectedTeams: AgenticTeamId[], priority: string
  ): Promise<{ success: boolean; id?: string; error?: string; }> {
    try {
      const pmoId = uuidv4();
      const agentOutputData = {
        userId: this.options.userId,
        agentType: 'PMO_Assessment_And_Requirements',
        title: `PMO Rqmts: ${title}`,
        content: content,
        fileUrl: fileUrl,
        createdAt: new Date(),
        metadata: {
          pmoId: pmoId,
          selectedTeams: selectedTeams.map(teamId => {
            const enumKey = Object.keys(AgenticTeamId).find(key => (AgenticTeamId as any)[key] === teamId);
            return enumKey || teamId.toString();
          }),
          priority: priority,
          originalRequestTitle: title,
        }
      };
      const result = await addAgentOutput(agentOutputData);

      const processResult = await processPMODocument({
        title: `Requirements Specification - ${title}`,
        content: content,
        pmoId: pmoId,
        userId: this.options.userId,
        category: 'PMO Generated Documents',
        metadata: {
          ...agentOutputData.metadata,
          agentOutputId: result.id,
          generatedAt: agentOutputData.createdAt.toISOString(),
          pdfFileUrl: fileUrl,
        }
      });
      if (!processResult.success) {
        console.error('Error processing PMO document for general storage:', processResult.error);
      }
      return { success: true, id: result.id };
    } catch (error: any) {
      console.error('Error saving agent output:', error);
      return { success: false, error: error.message || 'Save agent output failed' };
    }
  }

  private _updateStream(stage: PMOStreamUpdate['stage'], data: any, message: string): void {
    if (this.options.streamResponse && typeof this.options.onStreamUpdate === 'function') {
      try {
        this.options.onStreamUpdate({ stage, data, message, timestamp: new Date().toISOString() });
      } catch (e: any) {
        console.error("Error in onStreamUpdate callback:", e.message, "Stage:", stage);
      }
    }
  }
}